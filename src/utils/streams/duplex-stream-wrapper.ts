import type { ClientDuplexStream } from '@grpc/grpc-js'
import type { Awaitable } from '@kdt310722/utils/promise'
import { StreamWrapper, type StreamWrapperOptions } from './stream-wrapper'

export interface DuplexStreamWrapperOptions extends StreamWrapperOptions {
}

export class DuplexStreamWrapper<TRequest, TResponse, TStream extends ClientDuplexStream<TRequest, TResponse> = ClientDuplexStream<TRequest, TResponse>> extends StreamWrapper<TResponse, TStream> {
    public constructor(protected override readonly subscriber: () => Awaitable<TStream>, options: DuplexStreamWrapperOptions = {}) {
        super(subscriber, options)
    }

    public async write(data: TRequest) {}

    public override async close() {
        return this.end().then(() => super.close())
    }

    protected async end() {
        await this.stream?.then((stream) => stream.end())
    }
}
