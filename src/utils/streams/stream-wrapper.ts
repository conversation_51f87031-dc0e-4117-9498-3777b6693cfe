import type { ClientReadableStream } from '@grpc/grpc-js'
import { notNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { tryCatch } from '@kdt310722/utils/function'
import { resolveNestedOptions } from '@kdt310722/utils/object'
import { type Awaitable, createDeferred, tap, withTimeout } from '@kdt310722/utils/promise'
import { IdleTimeoutError, StreamError } from '../../errors'
import { isIgnorableGrpcError } from '../grpc'
import { ResubscribeManager, type ResubscribeManagerEvents, type ResubscribeManagerOptions } from './resubscribe-manager'
import type { OnStreamChangeFn, StreamWithClose } from './types'

export interface StreamWrapperTimeoutOptions {
    subscribe?: number
    close?: number
    idle?: number
}

export interface StreamWrapperOptions {
    timeout?: StreamWrapperTimeoutOptions
    resubscribe?: ResubscribeManagerOptions | boolean
    destroyOnError?: boolean
    destroyOnCloseFail?: boolean
}

export type StreamWrapperEvents<TData> = ResubscribeManagerEvents & {
    subscribed: () => void
    closed: (isExplicitly: boolean) => void
    data: (data: TData) => void
}

export class StreamWrapper<TData, TStream extends ClientReadableStream<TData> = ClientReadableStream<TData>> extends Emitter<StreamWrapperEvents<TData>, true> {
    protected readonly resubscribeManager: ResubscribeManager<TStream>

    protected readonly subscribeTimeout: number
    protected readonly closeTimeout: number
    protected readonly idleTimeout: number

    protected readonly destroyOnError: boolean
    protected readonly destroyOnCloseFail: boolean

    protected stream?: Promise<StreamWithClose<TStream>>
    protected closePromise?: Promise<void>

    protected idleTimer?: NodeJS.Timeout
    protected lastDataTime?: number

    public constructor(protected readonly subscriber: () => Awaitable<TStream>, { timeout = {}, destroyOnError = true, destroyOnCloseFail = true, resubscribe = true }: StreamWrapperOptions = {}) {
        super()

        this.subscribeTimeout = timeout.subscribe ?? 30_000
        this.closeTimeout = timeout.close ?? this.subscribeTimeout
        this.idleTimeout = timeout.idle ?? 60_000
        this.destroyOnError = destroyOnError
        this.destroyOnCloseFail = destroyOnCloseFail
        this.resubscribeManager = new ResubscribeManager<TStream>(resolveNestedOptions(resubscribe) || { enabled: false }, this, this.createStream.bind(this))
    }

    public async subscribe() {
        await this.closePromise
        await (this.stream ??= this.createStream((stream) => this.stream = notNullish(stream) ? Promise.resolve(stream) : undefined).then(tap(() => this.emit('subscribed'))))
    }

    public async close() {
        await this.stream?.then(async (stream) => this.closePromise ??= stream.close().finally(() => {
            this.stream = undefined
            this.closePromise = undefined
        }))
    }

    protected async createStream(onStreamChange?: OnStreamChangeFn<TStream>): Promise<StreamWithClose<TStream>> {
        const promise = createDeferred<void>()
        const stream = await this.subscriber()

        let metadataHandler: () => void
        let errorHandler: (error: Error) => void
        let closeHandler: () => void
        let endHandler: () => void
        let dataHandler: (data: TData) => void

        const cleanup = () => {
            stream.removeListener('metadata', metadataHandler)
            stream.removeListener('error', errorHandler)
            stream.removeListener('close', closeHandler)
            stream.removeListener('end', endHandler)
            stream.removeListener('data', dataHandler)
        }

        let isExplicitlyClosed = false

        stream.on('metadata', metadataHandler = () => promise.isSettled || promise.resolve())
        stream.on('error', errorHandler = (error) => (promise.isSettled ? this.handleError(stream, error) : promise.reject(error)))
        stream.on('close', closeHandler = () => (promise.isSettled ? this.handleClose(stream, cleanup, isExplicitlyClosed, onStreamChange) : promise.reject(new StreamError('Stream closed unexpectedly').withStream(stream))))
        stream.on('end', endHandler = () => (promise.isSettled ? stream.destroy() : promise.reject(new StreamError('Stream ended unexpectedly').withStream(stream))))
        stream.on('data', dataHandler = (data) => (promise.isSettled ? this.handleData(stream, data) : promise.resolve()))

        await withTimeout(promise, this.subscribeTimeout, () => new StreamError('Subscribe timeout').withStream(stream)).catch((error) => {
            cleanup()
            tryCatch(() => stream.destroy(), null)

            throw error
        })

        this.runIdleTimer(stream)

        const close = async () => {
            return Promise.resolve(isExplicitlyClosed = true).then(() => this.closeStream(stream))
        }

        return Object.assign(stream, { close })
    }

    protected async closeStream(stream: TStream) {
        const promise = createDeferred<void>()

        let closeHandler: () => void
        let errorHandler: (error: Error) => void

        stream.once('close', closeHandler = () => promise.resolve())
        stream.once('error', errorHandler = (error) => promise.reject(error))
        stream.cancel()

        const handleError = (error: Error) => {
            if (this.destroyOnCloseFail) {
                stream.destroy(error)
            } else {
                throw error
            }
        }

        await withTimeout(promise, this.closeTimeout, () => new StreamError('Close timeout').withStream(stream)).catch(handleError).finally(() => {
            stream.removeListener('close', closeHandler)
            stream.removeListener('error', errorHandler)
        })
    }

    protected handleData(stream: TStream, data: TData) {
        this.emit('data', data)
        this.lastDataTime = Date.now()
        this.runIdleTimer(stream)
    }

    protected runIdleTimer(stream: TStream) {
        if (this.idleTimer) {
            clearTimeout(this.idleTimer)
        }

        this.idleTimer = setTimeout(() => this.handleError(stream, new IdleTimeoutError().withStream(stream).withLastDataTime(this.lastDataTime)), this.idleTimeout)
    }

    protected handleError(stream: TStream, error: unknown) {
        if (isIgnorableGrpcError(error)) {
            return
        }

        this.emit('error', this.resubscribeManager.setLatestError(error))

        if (this.destroyOnError) {
            stream.destroy()
        }
    }

    protected handleClose(stream: TStream, cleanup: () => void, isExplicitly: boolean, onStreamChange?: OnStreamChangeFn<TStream>) {
        cleanup()

        this.emit('closed', isExplicitly)

        if (isExplicitly) {
            this.resubscribeManager.reset()
        } else {
            this.resubscribeManager.handleResubscribe(stream, onStreamChange)
        }

        clearTimeout(this.idleTimer)

        this.idleTimer = undefined
        this.lastDataTime = undefined
    }
}
